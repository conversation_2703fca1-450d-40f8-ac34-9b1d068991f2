"""
Section Handlers for Psychiatric Assessment System

This module contains handlers for individual assessment sections,
applying consistent form patterns and fixing checklist behavior issues.
"""

import streamlit as st
from typing import Dict, Any, List
from data_models import PSYCHIATRIC_MEDICATION_CLASSES
from ui_components import (
    create_form_section,
    create_enhanced_multiselect,
    create_enhanced_multiselect_with_guidance,
    create_toggle_button_grid,
    create_severity_slider,
    render_modern_css
)
from substance_section import render_substance_use_section

def render_demographics_section():
    """Render demographics section with form pattern"""
    st.markdown('<div class="section-header">👤 Demographics & Identifying Information</div>', unsafe_allow_html=True)
    st.info(f"Patient Code: **{st.session_state.patient_id}**")
    
    if 'demographics' not in st.session_state.patient_data:
        st.session_state.patient_data['demographics'] = {}
    
    demographics = st.session_state.patient_data['demographics']
    
    def demographics_form_content():
        col1, col2, col3 = st.columns(3)
        
        with col1:
            age = st.number_input("Age", min_value=0, max_value=120, value=demographics.get('age', 30))
            gender = st.selectbox(
                "Gender Identity",
                ["", "Male", "Female", "Non-binary", "Other", "Prefer not to say"],
                index=0 if not demographics.get('gender') else ["", "Male", "Female", "Non-binary", "Other", "Prefer not to say"].index(demographics.get('gender'))
            )
        
        with col2:
            marital_status = st.selectbox(
                "Marital Status",
                ["", "Single", "Married", "Divorced", "Widowed", "Separated", "Domestic partnership"],
                index=0 if not demographics.get('marital_status') else ["", "Single", "Married", "Divorced", "Widowed", "Separated", "Domestic partnership"].index(demographics.get('marital_status'))
            )
            education = st.selectbox(
                "Education Level",
                ["", "Less than high school", "High school", "Some college", "Bachelor's degree", "Graduate degree"],
                index=0 if not demographics.get('education') else ["", "Less than high school", "High school", "Some college", "Bachelor's degree", "Graduate degree"].index(demographics.get('education'))
            )
        
        with col3:
            employment = st.selectbox(
                "Employment Status",
                ["", "Employed full-time", "Employed part-time", "Unemployed", "Student", "Retired", "Disabled"],
                index=0 if not demographics.get('employment_status') else ["", "Employed full-time", "Employed part-time", "Unemployed", "Student", "Retired", "Disabled"].index(demographics.get('employment_status'))
            )
            living_situation = st.selectbox(
                "Living Situation",
                ["", "Lives alone", "Lives with family", "Lives with roommates", "Assisted living", "Homeless", "Other"],
                index=0 if not demographics.get('living_situation') else ["", "Lives alone", "Lives with family", "Lives with roommates", "Assisted living", "Homeless", "Other"].index(demographics.get('living_situation'))
            )
        
        return {
            'age': age,
            'gender': gender,
            'marital_status': marital_status,
            'education': education,
            'employment_status': employment,
            'living_situation': living_situation
        }
    
    if create_form_section(
        "Basic Demographics", 
        "demographics_form", 
        demographics_form_content,
        expanded=True,
        help_text="Collect basic demographic information for clinical context and ML training data."
    ):
        form_data = demographics_form_content()
        demographics.update(form_data)

def render_chief_complaint_section():
    """Render chief complaint section with form pattern"""
    st.markdown('<div class="section-header">📝 Chief Complaint & Referral</div>', unsafe_allow_html=True)
    
    if 'chief_complaint' not in st.session_state.patient_data:
        st.session_state.patient_data['chief_complaint'] = {}
    
    cc = st.session_state.patient_data['chief_complaint']
    
    def chief_complaint_form_content():
        presenting_problem = st.text_area(
            "Chief Complaint (in patient's own words)",
            value=cc.get('presenting_problem', ''),
            height=100,
            placeholder="What brings you here today?"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            duration = st.selectbox(
                "Duration of Current Problems",
                ["", "Days", "Weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"],
                index=0 if not cc.get('duration') else ["", "Days", "Weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"].index(cc.get('duration'))
            )
        
        with col2:
            severity = st.selectbox(
                "Severity of Current Problems",
                ["", "Mild", "Moderate", "Severe", "Very severe"],
                index=0 if not cc.get('severity') else ["", "Mild", "Moderate", "Severe", "Very severe"].index(cc.get('severity'))
            )
        
        referral_source = st.selectbox(
            "Referral Source",
            ["", "Self-referral", "Family/friend", "Primary care physician", "Emergency department", "Court-ordered", "Other healthcare provider"],
            index=0 if not cc.get('referral_source') else ["", "Self-referral", "Family/friend", "Primary care physician", "Emergency department", "Court-ordered", "Other healthcare provider"].index(cc.get('referral_source'))
        )
        
        return {
            'presenting_problem': presenting_problem,
            'duration': duration,
            'severity': severity,
            'referral_source': referral_source
        }
    
    if create_form_section(
        "Chief Complaint Details", 
        "chief_complaint_form", 
        chief_complaint_form_content,
        expanded=True,
        help_text="Document the primary reason for the assessment and current problem severity."
    ):
        form_data = chief_complaint_form_content()
        cc.update(form_data)

def render_history_present_illness_section():
    """Render history of present illness section - the successful pattern"""
    st.markdown('<div class="section-header">📋 History of Present Illness</div>', unsafe_allow_html=True)
    
    if 'history_present_illness' not in st.session_state.patient_data:
        st.session_state.patient_data['history_present_illness'] = {}
    
    hpi = st.session_state.patient_data['history_present_illness']
    
    # Mood Disorders Form
    def mood_disorders_content():
        if 'mood_symptoms' not in hpi:
            hpi['mood_symptoms'] = {}
        
        # Click-optimized depressive symptom assessment
        depressive_symptoms = create_toggle_button_grid(
            "Depressive Symptoms (Click to select)",
            ["Depressed mood", "Anhedonia", "Sleep disturbance", "Appetite changes",
             "Fatigue", "Concentration problems", "Guilt/worthlessness", "Psychomotor changes", "Suicidal thoughts"],
            default_values=hpi['mood_symptoms'].get('depressive_symptoms', []),
            columns=3,
            key_prefix="depressive_symptoms"
        )

        # Depression severity slider
        depression_severity = create_severity_slider(
            "Depression Severity",
            min_val=0, max_val=10,
            default_val=hpi['mood_symptoms'].get('depression_severity', 0),
            key="depression_severity_slider"
        )

        # Click-optimized manic symptom assessment
        manic_symptoms = create_toggle_button_grid(
            "Manic/Hypomanic Symptoms (Click to select)",
            ["Elevated mood", "Decreased need for sleep", "Grandiosity", "Racing thoughts",
             "Distractibility", "Increased activity", "Poor judgment", "Pressured speech"],
            default_values=hpi['mood_symptoms'].get('manic_symptoms', []),
            columns=3,
            key_prefix="manic_symptoms"
        )

        # Mania severity slider
        mania_severity = create_severity_slider(
            "Mania/Hypomania Severity",
            min_val=0, max_val=10,
            default_val=hpi['mood_symptoms'].get('mania_severity', 0),
            key="mania_severity_slider"
        )

        return {
            'depressive_symptoms': depressive_symptoms,
            'depression_severity': depression_severity,
            'manic_symptoms': manic_symptoms,
            'mania_severity': mania_severity
        }
    
    if create_form_section(
        "🌙 Mood Disorders", 
        "mood_disorders_form", 
        mood_disorders_content,
        help_text="Assess for major depressive episodes, manic episodes, and mixed states."
    ):
        form_data = mood_disorders_content()
        if 'mood_symptoms' not in hpi:
            hpi['mood_symptoms'] = {}
        hpi['mood_symptoms'].update(form_data)
    
    # Anxiety Disorders Form
    def anxiety_disorders_content():
        if 'anxiety_symptoms' not in hpi:
            hpi['anxiety_symptoms'] = {}
        
        # Click-optimized anxiety symptom assessment
        anxiety_symptoms = create_toggle_button_grid(
            "Anxiety Symptoms (Click to select)",
            ["Excessive worry", "Restlessness", "Fatigue", "Concentration problems",
             "Irritability", "Muscle tension", "Sleep disturbance", "Panic attacks"],
            default_values=hpi['anxiety_symptoms'].get('anxiety_symptoms', []),
            columns=3,
            key_prefix="anxiety_symptoms"
        )

        # Severity slider for anxiety
        anxiety_severity = create_severity_slider(
            "Overall Anxiety Severity",
            min_val=0, max_val=10,
            default_val=hpi['anxiety_symptoms'].get('anxiety_severity', 0),
            key="anxiety_severity_slider"
        )

        # Click-optimized panic symptom assessment
        panic_symptoms = create_toggle_button_grid(
            "Panic Attack Symptoms (Click to select)",
            ["Palpitations", "Sweating", "Trembling", "Shortness of breath",
             "Choking sensation", "Chest pain", "Nausea", "Dizziness", "Fear of dying", "Fear of losing control"],
            default_values=hpi['anxiety_symptoms'].get('panic_symptoms', []),
            columns=3,
            key_prefix="panic_symptoms"
        )

        # Panic frequency slider
        panic_frequency = create_severity_slider(
            "Panic Attack Frequency (per week)",
            min_val=0, max_val=20,
            default_val=hpi['anxiety_symptoms'].get('panic_frequency', 0),
            key="panic_frequency_slider"
        )

        return {
            'anxiety_symptoms': anxiety_symptoms,
            'anxiety_severity': anxiety_severity,
            'panic_symptoms': panic_symptoms,
            'panic_frequency': panic_frequency
        }
    
    if create_form_section(
        "😰 Anxiety Disorders", 
        "anxiety_disorders_form", 
        anxiety_disorders_content,
        help_text="Assess for generalized anxiety disorder, panic disorder, and specific phobias."
    ):
        form_data = anxiety_disorders_content()
        if 'anxiety_symptoms' not in hpi:
            hpi['anxiety_symptoms'] = {}
        hpi['anxiety_symptoms'].update(form_data)

def render_past_psychiatric_history_section():
    """Render past psychiatric history section with fixed form patterns"""
    render_modern_css()
    
    st.markdown('<div class="section-header">🏥 Past Psychiatric History</div>', unsafe_allow_html=True)
    
    if 'past_psychiatric_history' not in st.session_state.patient_data:
        st.session_state.patient_data['past_psychiatric_history'] = {}
    
    pph = st.session_state.patient_data['past_psychiatric_history']
    
    # Previous Diagnoses Form - FIXED
    def previous_diagnoses_content():
        diagnoses = create_enhanced_multiselect(
            "Previous Psychiatric Diagnoses",
            ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder", "Persistent Depressive Disorder", 
             "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder", "Specific Phobia", 
             "Obsessive-Compulsive Disorder", "PTSD", "Acute Stress Disorder", "Adjustment Disorder", 
             "Schizophrenia", "Schizoaffective Disorder", "Brief Psychotic Disorder", "Delusional Disorder", 
             "Substance Use Disorder", "Alcohol Use Disorder", "ADHD", "Autism Spectrum Disorder", 
             "Anorexia Nervosa", "Bulimia Nervosa", "Binge Eating Disorder", "Borderline Personality Disorder", 
             "Antisocial Personality Disorder", "Other Personality Disorder", "Intellectual Disability", 
             "Dementia", "Other"],
            default_values=pph.get('previous_diagnoses', []),
            help_text="Select all previous psychiatric diagnoses"
        )
        
        return {'previous_diagnoses': diagnoses}
    
    if create_form_section(
        "🏷️ Previous Psychiatric Diagnoses", 
        "previous_diagnoses_form", 
        previous_diagnoses_content,
        expanded=True,
        help_text="Document all previous psychiatric diagnoses to understand treatment history and course of illness."
    ):
        form_data = previous_diagnoses_content()
        pph.update(form_data)
    
    # Treatment History Form - FIXED
    def treatment_history_content():
        col1, col2 = st.columns(2)
        
        with col1:
            hospitalizations = st.selectbox(
                "Psychiatric Hospitalizations",
                ["None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"],
                index=0 if not pph.get('hospitalizations') else ["None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"].index(pph.get('hospitalizations'))
            )
            
            hospitalization_reasons = create_enhanced_multiselect(
                "Reasons for Hospitalization",
                ["Suicidal ideation/attempt", "Homicidal ideation", "Psychosis", "Severe depression", 
                 "Mania/hypomania", "Substance intoxication", "Substance withdrawal", "Self-harm", 
                 "Inability to care for self", "Medication adjustment", "Other"],
                default_values=pph.get('hospitalization_reasons', [])
            )
        
        with col2:
            outpatient_treatment = create_enhanced_multiselect(
                "Previous Outpatient Treatment",
                ["Individual therapy", "Group therapy", "Family therapy", "Couples therapy", "Medication management", 
                 "Intensive outpatient program", "Partial hospitalization", "Day treatment", "Case management", 
                 "Peer support", "Support groups", "Other"],
                default_values=pph.get('outpatient_treatment', [])
            )
            
            therapy_types = create_enhanced_multiselect(
                "Types of Therapy Received",
                ["Cognitive Behavioral Therapy (CBT)", "Dialectical Behavior Therapy (DBT)", 
                 "Psychodynamic therapy", "Interpersonal therapy", "EMDR", "Exposure therapy", 
                 "Acceptance and Commitment Therapy", "Mindfulness-based therapy", "Family therapy", 
                 "Group therapy", "Art/music therapy", "Other"],
                default_values=pph.get('therapy_types', [])
            )
        
        treatment_response = st.selectbox(
            "Overall Response to Previous Treatment",
            ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"],
            index=0 if not pph.get('treatment_response') else ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"].index(pph.get('treatment_response'))
        )
        
        return {
            'hospitalizations': hospitalizations,
            'hospitalization_reasons': hospitalization_reasons,
            'outpatient_treatment': outpatient_treatment,
            'therapy_types': therapy_types,
            'treatment_response': treatment_response
        }
    
    if create_form_section(
        "🏥 Treatment History", 
        "treatment_history_form", 
        treatment_history_content,
        expanded=True,
        help_text="Document previous psychiatric treatments including hospitalizations, therapy, and overall response."
    ):
        form_data = treatment_history_content()
        pph.update(form_data)
    
    # Medication History - FIXED with enhanced form pattern
    render_medication_history_section(pph)

def render_medication_history_section(pph: Dict[str, Any]):
    """Render medication history section with enhanced form patterns"""
    st.markdown("### 💊 Comprehensive Psychiatric Medication History")
    
    if 'medication_history' not in pph:
        pph['medication_history'] = {}
    
    # Medication class selection
    def medication_classes_content():
        tried_classes = create_enhanced_multiselect(
            "Medication Classes Previously Tried",
            list(PSYCHIATRIC_MEDICATION_CLASSES.keys()),
            default_values=list(pph['medication_history'].keys()),
            help_text="Select all medication classes that have been tried"
        )
        
        return {'tried_classes': tried_classes}
    
    if create_form_section(
        "💊 Medication Classes", 
        "medication_classes_form", 
        medication_classes_content,
        expanded=True,
        help_text="Select medication classes to document detailed trial history."
    ):
        form_data = medication_classes_content()
        
        # Update medication history structure
        current_classes = set(pph['medication_history'].keys())
        selected_classes = set(form_data['tried_classes'])
        
        # Add new classes
        for med_class in selected_classes - current_classes:
            pph['medication_history'][med_class] = {'trials': []}
        
        # Remove unselected classes
        for med_class in current_classes - selected_classes:
            del pph['medication_history'][med_class]
    
    # Detailed medication trials for each selected class
    for med_class in pph['medication_history'].keys():
        render_medication_class_details(med_class, pph['medication_history'][med_class])

def render_medication_class_details(med_class: str, med_data: Dict[str, Any]):
    """Render detailed medication trial information for a specific class"""
    class_display_name = med_class.replace('_', ' ')

    def medication_details_content():
        st.markdown(f"#### {class_display_name} Trials")

        # Add trial button
        if st.button(f"➕ Add {class_display_name} Trial", key=f"add_{med_class}_trial"):
            if 'trials' not in med_data:
                med_data['trials'] = []
            med_data['trials'].append({
                'medication': '',
                'response_rating': 1,
                'side_effects': [],
                'duration_weeks': 0,
                'discontinuation_reason': '',
                'adherence_rating': 1
            })

        # Display existing trials
        if 'trials' in med_data and med_data['trials']:
            for i, trial in enumerate(med_data['trials']):
                st.markdown(f"**Trial {i+1}:**")
                col1, col2, col3 = st.columns(3)

                with col1:
                    trial['medication'] = st.text_input(
                        "Medication",
                        value=trial.get('medication', ''),
                        key=f"{med_class}_med_{i}"
                    )
                    trial['response_rating'] = st.slider(
                        "Response (1=Poor, 5=Excellent)",
                        1, 5, trial.get('response_rating', 1),
                        key=f"{med_class}_response_{i}"
                    )

                with col2:
                    trial['duration_weeks'] = st.number_input(
                        "Duration (weeks)",
                        min_value=0, max_value=520,
                        value=trial.get('duration_weeks', 0),
                        key=f"{med_class}_duration_{i}"
                    )
                    trial['adherence_rating'] = st.slider(
                        "Adherence (1=Poor, 5=Excellent)",
                        1, 5, trial.get('adherence_rating', 1),
                        key=f"{med_class}_adherence_{i}"
                    )

                with col3:
                    trial['side_effects'] = create_enhanced_multiselect(
                        "Side Effects",
                        ["Nausea", "Headache", "Dizziness", "Drowsiness", "Insomnia",
                         "Weight gain", "Weight loss", "Sexual dysfunction", "Dry mouth", "Other"],
                        default_values=trial.get('side_effects', []),
                        key=f"{med_class}_side_effects_{i}"
                    )

                    if st.button(f"🗑️ Remove Trial {i+1}", key=f"remove_{med_class}_{i}"):
                        med_data['trials'].pop(i)
                        st.rerun()

                st.markdown("---")

        # Overall class response
        overall_response = st.selectbox(
            f"Overall Response to {class_display_name}",
            ["", "Excellent", "Good", "Partial", "Poor", "No response"],
            index=0 if not med_data.get('overall_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response"].index(med_data.get('overall_response')),
            key=f"{med_class}_overall"
        )

        return {'overall_response': overall_response}

    if create_form_section(
        f"💊 {class_display_name} Details",
        f"medication_details_{med_class}",
        medication_details_content,
        expanded=bool(med_data.get('trials')),
        help_text=f"Document detailed trial history for {class_display_name} including response, side effects, and adherence."
    ):
        form_data = medication_details_content()
        med_data.update(form_data)

# Additional Assessment Sections

def render_past_medical_history_section():
    """Render past medical history section"""
    st.markdown('<div class="section-header">🏥 Past Medical History</div>', unsafe_allow_html=True)

    if 'past_medical_history' not in st.session_state.patient_data:
        st.session_state.patient_data['past_medical_history'] = {}

    pmh = st.session_state.patient_data['past_medical_history']

    def medical_conditions_content():
        medical_conditions = create_enhanced_multiselect(
            "Current/Past Medical Conditions",
            ["Diabetes", "Hypertension", "Heart disease", "Stroke", "Cancer", "Kidney disease",
             "Liver disease", "Thyroid disorder", "Seizure disorder", "Head injury",
             "Chronic pain", "Autoimmune disorder", "Sleep apnea", "COPD", "Asthma", "Other"],
            default_values=pmh.get('medical_conditions', []),
            help_text="Select all relevant medical conditions"
        )

        col1, col2 = st.columns(2)
        with col1:
            current_medications = st.text_area(
                "Current Medications",
                value=pmh.get('current_medications', ''),
                height=100,
                placeholder="List current medications with dosages"
            )

        with col2:
            allergies = st.text_area(
                "Allergies/Adverse Reactions",
                value=pmh.get('allergies', ''),
                height=100,
                placeholder="List drug allergies and reactions"
            )

        return {
            'medical_conditions': medical_conditions,
            'current_medications': current_medications,
            'allergies': allergies
        }

    if create_form_section(
        "🏥 Medical History",
        "past_medical_history_form",
        medical_conditions_content,
        expanded=True,
        help_text="Document relevant medical conditions that may impact psychiatric treatment."
    ):
        form_data = medical_conditions_content()
        pmh.update(form_data)

def render_family_history_section():
    """Render family history section"""
    st.markdown('<div class="section-header">👨‍👩‍👧‍👦 Family History</div>', unsafe_allow_html=True)

    if 'family_history' not in st.session_state.patient_data:
        st.session_state.patient_data['family_history'] = {}

    fh = st.session_state.patient_data['family_history']

    def family_psychiatric_content():
        psychiatric_conditions = create_enhanced_multiselect(
            "Family Psychiatric History",
            ["Depression", "Bipolar disorder", "Anxiety disorders", "Schizophrenia",
             "Substance use disorders", "Suicide", "ADHD", "Autism", "Eating disorders",
             "Personality disorders", "Other psychiatric conditions"],
            default_values=fh.get('psychiatric_conditions', []),
            help_text="Select psychiatric conditions present in family members"
        )

        col1, col2 = st.columns(2)
        with col1:
            medical_conditions = create_enhanced_multiselect(
                "Family Medical History",
                ["Heart disease", "Diabetes", "Cancer", "Stroke", "Hypertension",
                 "Kidney disease", "Liver disease", "Thyroid disorder", "Other"],
                default_values=fh.get('medical_conditions', [])
            )

        with col2:
            family_details = st.text_area(
                "Additional Family History Details",
                value=fh.get('family_details', ''),
                height=100,
                placeholder="Specify which family members and additional details"
            )

        return {
            'psychiatric_conditions': psychiatric_conditions,
            'medical_conditions': medical_conditions,
            'family_details': family_details
        }

    if create_form_section(
        "👨‍👩‍👧‍👦 Family History",
        "family_history_form",
        family_psychiatric_content,
        expanded=True,
        help_text="Family history helps identify genetic predispositions and risk factors."
    ):
        form_data = family_psychiatric_content()
        fh.update(form_data)

def render_social_developmental_history_section():
    """Render social and developmental history section"""
    st.markdown('<div class="section-header">🏠 Social & Developmental History</div>', unsafe_allow_html=True)

    if 'social_developmental_history' not in st.session_state.patient_data:
        st.session_state.patient_data['social_developmental_history'] = {}

    sdh = st.session_state.patient_data['social_developmental_history']

    def social_history_content():
        col1, col2 = st.columns(2)

        with col1:
            relationship_status = st.selectbox(
                "Current Relationship Status",
                ["", "Single", "In relationship", "Married", "Divorced", "Widowed", "Separated"],
                index=0 if not sdh.get('relationship_status') else ["", "Single", "In relationship", "Married", "Divorced", "Widowed", "Separated"].index(sdh.get('relationship_status'))
            )

            support_system = create_enhanced_multiselect(
                "Support System",
                ["Family", "Friends", "Partner/Spouse", "Religious community", "Support groups",
                 "Mental health professionals", "Case manager", "Limited support", "No support"],
                default_values=sdh.get('support_system', [])
            )

        with col2:
            trauma_history = create_enhanced_multiselect(
                "Trauma History",
                ["Physical abuse", "Sexual abuse", "Emotional abuse", "Neglect", "Domestic violence",
                 "Combat trauma", "Accidents", "Natural disasters", "Medical trauma", "Other trauma"],
                default_values=sdh.get('trauma_history', []),
                help_text="Select all relevant trauma experiences"
            )

            legal_issues = create_enhanced_multiselect(
                "Legal History",
                ["No legal issues", "DUI/DWI", "Drug-related charges", "Assault", "Theft",
                 "Domestic violence", "Probation", "Incarceration", "Other"],
                default_values=sdh.get('legal_issues', [])
            )

        developmental_concerns = st.text_area(
            "Developmental History/Concerns",
            value=sdh.get('developmental_concerns', ''),
            height=80,
            placeholder="Early development, learning disabilities, etc."
        )

        return {
            'relationship_status': relationship_status,
            'support_system': support_system,
            'trauma_history': trauma_history,
            'legal_issues': legal_issues,
            'developmental_concerns': developmental_concerns
        }

    if create_form_section(
        "🏠 Social & Developmental History",
        "social_developmental_form",
        social_history_content,
        expanded=True,
        help_text="Social and developmental factors significantly impact mental health and treatment planning."
    ):
        form_data = social_history_content()
        sdh.update(form_data)

def render_mental_status_cognitive_section():
    """Render combined mental status and cognitive assessment section"""
    st.markdown('<div class="section-header">🧠 Mental Status & Cognitive Examination</div>', unsafe_allow_html=True)

    # Initialize both data structures
    if 'mental_state_examination' not in st.session_state.patient_data:
        st.session_state.patient_data['mental_state_examination'] = {}
    if 'cognitive_assessment' not in st.session_state.patient_data:
        st.session_state.patient_data['cognitive_assessment'] = {}

    mse = st.session_state.patient_data['mental_state_examination']
    cog = st.session_state.patient_data['cognitive_assessment']

    def appearance_behavior_content():
        col1, col2, col3 = st.columns(3)

        with col1:
            appearance = st.selectbox(
                "Appearance",
                ["", "Well-groomed", "Disheveled", "Bizarre", "Inappropriate dress", "Poor hygiene"],
                index=0 if not mse.get('appearance') else ["", "Well-groomed", "Disheveled", "Bizarre", "Inappropriate dress", "Poor hygiene"].index(mse.get('appearance'))
            )

            behavior = create_enhanced_multiselect(
                "Behavior",
                ["Cooperative", "Agitated", "Withdrawn", "Hostile", "Bizarre", "Restless",
                 "Psychomotor retardation", "Catatonic", "Normal"],
                default_values=mse.get('behavior', [])
            )

        with col2:
            speech = create_enhanced_multiselect(
                "Speech",
                ["Normal rate", "Rapid", "Slow", "Loud", "Soft", "Pressured",
                 "Monotone", "Slurred", "Clear"],
                default_values=mse.get('speech', [])
            )

            mood = st.selectbox(
                "Mood (subjective)",
                ["", "Euthymic", "Depressed", "Anxious", "Irritable", "Elevated", "Labile"],
                index=0 if not mse.get('mood') else ["", "Euthymic", "Depressed", "Anxious", "Irritable", "Elevated", "Labile"].index(mse.get('mood'))
            )

        with col3:
            affect = create_enhanced_multiselect(
                "Affect (objective)",
                ["Appropriate", "Inappropriate", "Flat", "Blunted", "Labile", "Restricted", "Expansive"],
                default_values=mse.get('affect', [])
            )

            thought_process = create_enhanced_multiselect(
                "Thought Process",
                ["Linear", "Tangential", "Circumstantial", "Flight of ideas", "Loose associations",
                 "Thought blocking", "Perseveration", "Disorganized"],
                default_values=mse.get('thought_process', [])
            )

        return {
            'appearance': appearance,
            'behavior': behavior,
            'speech': speech,
            'mood': mood,
            'affect': affect,
            'thought_process': thought_process
        }

    if create_form_section(
        "👁️ Appearance & Behavior",
        "appearance_behavior_form",
        appearance_behavior_content,
        expanded=True,
        help_text="Systematic observation of patient's presentation and behavior during assessment."
    ):
        form_data = appearance_behavior_content()
        mse.update(form_data)

    def thought_content_content():
        col1, col2 = st.columns(2)

        with col1:
            thought_content = create_enhanced_multiselect(
                "Thought Content",
                ["Normal", "Obsessions", "Compulsions", "Phobias", "Suicidal ideation",
                 "Homicidal ideation", "Paranoid thoughts", "Grandiose thoughts", "Somatic concerns"],
                default_values=mse.get('thought_content', [])
            )

            perceptual_disturbances = create_enhanced_multiselect(
                "Perceptual Disturbances",
                ["None", "Auditory hallucinations", "Visual hallucinations", "Tactile hallucinations",
                 "Olfactory hallucinations", "Gustatory hallucinations", "Illusions"],
                default_values=mse.get('perceptual_disturbances', [])
            )

        with col2:
            delusions = create_enhanced_multiselect(
                "Delusions",
                ["None", "Persecutory", "Grandiose", "Somatic", "Religious", "Erotomanic",
                 "Jealous", "Nihilistic", "Bizarre"],
                default_values=mse.get('delusions', [])
            )

            insight = st.selectbox(
                "Insight",
                ["", "Good", "Fair", "Poor", "Absent"],
                index=0 if not mse.get('insight') else ["", "Good", "Fair", "Poor", "Absent"].index(mse.get('insight'))
            )

        judgment = st.selectbox(
            "Judgment",
            ["", "Good", "Fair", "Poor", "Impaired"],
            index=0 if not mse.get('judgment') else ["", "Good", "Fair", "Poor", "Impaired"].index(mse.get('judgment'))
        )

        return {
            'thought_content': thought_content,
            'perceptual_disturbances': perceptual_disturbances,
            'delusions': delusions,
            'insight': insight,
            'judgment': judgment
        }

    if create_form_section(
        "💭 Thought Content & Perception",
        "thought_content_form",
        thought_content_content,
        expanded=True,
        help_text="Assessment of thought content, perceptual experiences, and reality testing."
    ):
        form_data = thought_content_content()
        mse.update(form_data)

    # Add cognitive assessment content to the combined section
    def cognitive_content():
        st.markdown("#### 🧩 Cognitive Functions")
        col1, col2, col3 = st.columns(3)

        with col1:
            orientation = create_enhanced_multiselect(
                "Orientation",
                ["Person", "Place", "Time", "Situation"],
                default_values=cog.get('orientation', []),
                help_text="Select all areas where patient is oriented"
            )

            attention = st.selectbox(
                "Attention/Concentration",
                ["", "Normal", "Mildly impaired", "Moderately impaired", "Severely impaired"],
                index=0 if not cog.get('attention') else ["", "Normal", "Mildly impaired", "Moderately impaired", "Severely impaired"].index(cog.get('attention'))
            )

        with col2:
            memory = create_enhanced_multiselect(
                "Memory",
                ["Immediate recall intact", "Recent memory intact", "Remote memory intact",
                 "Immediate recall impaired", "Recent memory impaired", "Remote memory impaired"],
                default_values=cog.get('memory', [])
            )

            abstract_thinking = st.selectbox(
                "Abstract Thinking",
                ["", "Normal", "Concrete", "Impaired"],
                index=0 if not cog.get('abstract_thinking') else ["", "Normal", "Concrete", "Impaired"].index(cog.get('abstract_thinking'))
            )

        with col3:
            intelligence = st.selectbox(
                "Estimated Intelligence",
                ["", "Above average", "Average", "Below average", "Significantly impaired"],
                index=0 if not cog.get('intelligence') else ["", "Above average", "Average", "Below average", "Significantly impaired"].index(cog.get('intelligence'))
            )

            cognitive_concerns = st.text_area(
                "Additional Cognitive Concerns",
                value=cog.get('cognitive_concerns', ''),
                height=80,
                placeholder="Any other cognitive observations or concerns"
            )

        return {
            'orientation': orientation,
            'attention': attention,
            'memory': memory,
            'abstract_thinking': abstract_thinking,
            'intelligence': intelligence,
            'cognitive_concerns': cognitive_concerns
        }

    if create_form_section(
        "🧩 Cognitive Functions",
        "cognitive_assessment_form",
        cognitive_content,
        expanded=True,
        help_text="Assessment of cognitive functions including orientation, memory, attention, and abstract thinking."
    ):
        form_data = cognitive_content()
        cog.update(form_data)

def render_risk_assessment_section():
    """Render risk assessment section"""
    st.markdown('<div class="section-header">⚠️ Risk Assessment</div>', unsafe_allow_html=True)

    if 'risk_assessment' not in st.session_state.patient_data:
        st.session_state.patient_data['risk_assessment'] = {}

    risk = st.session_state.patient_data['risk_assessment']

    def suicide_risk_content():
        col1, col2 = st.columns(2)

        with col1:
            suicidal_ideation = st.selectbox(
                "Suicidal Ideation",
                ["None", "Passive", "Active without plan", "Active with plan", "Active with intent"],
                index=0 if not risk.get('suicidal_ideation') else ["None", "Passive", "Active without plan", "Active with plan", "Active with intent"].index(risk.get('suicidal_ideation'))
            )

            suicide_risk_factors = create_enhanced_multiselect(
                "Suicide Risk Factors",
                ["Previous attempts", "Family history of suicide", "Substance use", "Social isolation",
                 "Chronic illness", "Recent loss", "Access to means", "Impulsivity", "Hopelessness"],
                default_values=risk.get('suicide_risk_factors', [])
            )

        with col2:
            protective_factors = create_enhanced_multiselect(
                "Protective Factors",
                ["Strong support system", "Religious beliefs", "Responsibility to family",
                 "Future plans", "Treatment engagement", "Coping skills", "Reasons for living"],
                default_values=risk.get('protective_factors', [])
            )

            overall_suicide_risk = st.selectbox(
                "Overall Suicide Risk Level",
                ["Low", "Moderate", "High", "Imminent"],
                index=0 if not risk.get('overall_suicide_risk') else ["Low", "Moderate", "High", "Imminent"].index(risk.get('overall_suicide_risk'))
            )

        return {
            'suicidal_ideation': suicidal_ideation,
            'suicide_risk_factors': suicide_risk_factors,
            'protective_factors': protective_factors,
            'overall_suicide_risk': overall_suicide_risk
        }

    if create_form_section(
        "🚨 Suicide Risk Assessment",
        "suicide_risk_form",
        suicide_risk_content,
        expanded=True,
        help_text="Comprehensive suicide risk assessment is critical for patient safety and treatment planning."
    ):
        form_data = suicide_risk_content()
        risk.update(form_data)

    def violence_risk_content():
        col1, col2 = st.columns(2)

        with col1:
            homicidal_ideation = st.selectbox(
                "Homicidal/Violence Ideation",
                ["None", "Vague thoughts", "Specific thoughts", "Plan present", "Intent present"],
                index=0 if not risk.get('homicidal_ideation') else ["None", "Vague thoughts", "Specific thoughts", "Plan present", "Intent present"].index(risk.get('homicidal_ideation'))
            )

            violence_risk_factors = create_enhanced_multiselect(
                "Violence Risk Factors",
                ["History of violence", "Substance use", "Paranoid delusions", "Command hallucinations",
                 "Impulsivity", "Antisocial traits", "Access to weapons", "Recent stressors"],
                default_values=risk.get('violence_risk_factors', [])
            )

        with col2:
            target_identified = st.checkbox(
                "Specific Target Identified",
                value=risk.get('target_identified', False)
            )

            overall_violence_risk = st.selectbox(
                "Overall Violence Risk Level",
                ["Low", "Moderate", "High", "Imminent"],
                index=0 if not risk.get('overall_violence_risk') else ["Low", "Moderate", "High", "Imminent"].index(risk.get('overall_violence_risk'))
            )

        return {
            'homicidal_ideation': homicidal_ideation,
            'violence_risk_factors': violence_risk_factors,
            'target_identified': target_identified,
            'overall_violence_risk': overall_violence_risk
        }

    if create_form_section(
        "⚔️ Violence Risk Assessment",
        "violence_risk_form",
        violence_risk_content,
        expanded=True,
        help_text="Assessment of risk for violence toward others, including duty to warn considerations."
    ):
        form_data = violence_risk_content()
        risk.update(form_data)

def render_laboratory_investigations_section():
    """Render enhanced laboratory and investigations section with clinical interpretation"""
    st.markdown('<div class="section-header">🔬 Laboratory & Investigations</div>', unsafe_allow_html=True)

    if 'laboratory_investigations' not in st.session_state.patient_data:
        st.session_state.patient_data['laboratory_investigations'] = {}

    lab = st.session_state.patient_data['laboratory_investigations']

    # Clinical guidance for laboratory interpretation
    st.markdown("""
    <div class="clinical-guidance">
        <h4>💡 Clinical Guidance - Laboratory Assessment</h4>
        <p>Laboratory tests help rule out medical causes of psychiatric symptoms and monitor treatment effects.
        Consider thyroid function, B12/folate, metabolic panels, and substance screening based on presentation.</p>
    </div>
    """, unsafe_allow_html=True)

    def laboratory_content():
        col1, col2 = st.columns(2)

        with col1:
            labs_ordered = create_enhanced_multiselect(
                "Laboratory Tests Ordered/Reviewed",
                ["CBC", "CMP", "TSH", "B12", "Folate", "Vitamin D", "Toxicology screen",
                 "Lithium level", "Valproate level", "HbA1c", "Lipid panel", "Other"],
                default_values=lab.get('labs_ordered', []),
                help_text="Select relevant laboratory tests"
            )

            imaging_studies = create_enhanced_multiselect(
                "Imaging Studies",
                ["None", "CT head", "MRI brain", "Chest X-ray", "EKG", "EEG", "Other"],
                default_values=lab.get('imaging_studies', [])
            )

        with col2:
            # Enhanced result interpretation section
            st.markdown("##### 📊 Results & Clinical Interpretation")

            abnormal_findings = st.text_area(
                "Abnormal Findings",
                value=lab.get('abnormal_findings', ''),
                height=80,
                placeholder="Document any abnormal laboratory or imaging findings"
            )

            clinical_significance = st.text_area(
                "Clinical Significance & Psychiatric Correlation",
                value=lab.get('clinical_significance', ''),
                height=80,
                placeholder="How do these results impact psychiatric diagnosis and treatment?"
            )

            # Monitoring parameters for psychiatric medications
            monitoring_needed = create_enhanced_multiselect(
                "Monitoring Parameters Required",
                ["Lithium levels", "Valproate levels", "Liver function", "Kidney function",
                 "Thyroid function", "Metabolic monitoring", "Cardiac monitoring", "None"],
                default_values=lab.get('monitoring_needed', []),
                help_text="Select ongoing monitoring requirements"
            )

        return {
            'labs_ordered': labs_ordered,
            'imaging_studies': imaging_studies,
            'abnormal_findings': abnormal_findings,
            'clinical_significance': clinical_significance,
            'monitoring_needed': monitoring_needed
        }

    if create_form_section(
        "🔬 Laboratory & Investigations",
        "laboratory_form",
        laboratory_content,
        expanded=True,
        help_text="Document relevant laboratory tests and investigations to rule out medical causes."
    ):
        form_data = laboratory_content()
        lab.update(form_data)

def render_clinical_scales_section():
    """Render clinical scales and ratings section"""
    st.markdown('<div class="section-header">📊 Clinical Scales & Ratings</div>', unsafe_allow_html=True)

    if 'clinical_scales' not in st.session_state.patient_data:
        st.session_state.patient_data['clinical_scales'] = {}

    scales = st.session_state.patient_data['clinical_scales']

    def clinical_scales_content():
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("**Depression Scales**")
            phq9_score = st.number_input(
                "PHQ-9 Total Score",
                min_value=0, max_value=27,
                value=scales.get('phq9_score', 0),
                help="Patient Health Questionnaire-9"
            )

            phq9_severity = st.selectbox(
                "PHQ-9 Severity",
                ["", "Minimal (0-4)", "Mild (5-9)", "Moderate (10-14)", "Moderately severe (15-19)", "Severe (20-27)"],
                index=0 if not scales.get('phq9_severity') else ["", "Minimal (0-4)", "Mild (5-9)", "Moderate (10-14)", "Moderately severe (15-19)", "Severe (20-27)"].index(scales.get('phq9_severity'))
            )

        with col2:
            st.markdown("**Anxiety Scales**")
            gad7_score = st.number_input(
                "GAD-7 Total Score",
                min_value=0, max_value=21,
                value=scales.get('gad7_score', 0),
                help="Generalized Anxiety Disorder-7"
            )

            gad7_severity = st.selectbox(
                "GAD-7 Severity",
                ["", "Minimal (0-4)", "Mild (5-9)", "Moderate (10-14)", "Severe (15-21)"],
                index=0 if not scales.get('gad7_severity') else ["", "Minimal (0-4)", "Mild (5-9)", "Moderate (10-14)", "Severe (15-21)"].index(scales.get('gad7_severity'))
            )

        with col3:
            st.markdown("**Other Scales**")
            other_scales = create_enhanced_multiselect(
                "Additional Scales Used",
                ["MMSE", "MoCA", "AUDIT", "CAGE", "PCL-5", "YBOCS", "MADRS", "HAM-D", "BPRS", "Other"],
                default_values=scales.get('other_scales', [])
            )

            scale_notes = st.text_area(
                "Scale Results/Notes",
                value=scales.get('scale_notes', ''),
                height=80,
                placeholder="Additional scale scores and interpretations"
            )

        return {
            'phq9_score': phq9_score,
            'phq9_severity': phq9_severity,
            'gad7_score': gad7_score,
            'gad7_severity': gad7_severity,
            'other_scales': other_scales,
            'scale_notes': scale_notes
        }

    if create_form_section(
        "📊 Clinical Rating Scales",
        "clinical_scales_form",
        clinical_scales_content,
        expanded=True,
        help_text="Standardized rating scales provide objective measures of symptom severity."
    ):
        form_data = clinical_scales_content()
        scales.update(form_data)

def render_diagnostic_formulation_section():
    """Render enhanced diagnostic formulation section with clinical depth"""
    st.markdown('<div class="section-header">🎯 Comprehensive Diagnostic Formulation</div>', unsafe_allow_html=True)

    if 'diagnostic_formulation' not in st.session_state.patient_data:
        st.session_state.patient_data['diagnostic_formulation'] = {}

    diag = st.session_state.patient_data['diagnostic_formulation']

    # Clinical guidance for diagnostic formulation
    st.markdown("""
    <div class="clinical-guidance">
        <h4>💡 Clinical Guidance - Diagnostic Formulation</h4>
        <p>Comprehensive diagnostic assessment should include primary diagnosis with specifiers,
        secondary/comorbid conditions, differential diagnoses to rule out, and confidence levels.
        Consider biopsychosocial factors and clinical reasoning.</p>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced Primary Diagnosis Section with Clinical Depth
    def primary_diagnosis_content():
        st.markdown("#### 🎯 Primary Diagnosis & Specifiers")
        col1, col2 = st.columns(2)

        with col1:
            primary_diagnosis = st.selectbox(
                "Primary Diagnosis",
                ["", "Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder",
                 "Generalized Anxiety Disorder", "Panic Disorder", "PTSD", "OCD", "ADHD",
                 "Schizophrenia", "Schizoaffective Disorder", "Substance Use Disorder",
                 "Autism Spectrum Disorder", "Eating Disorder", "Sleep-Wake Disorder", "Other"],
                index=0 if not diag.get('primary_diagnosis') else ["", "Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder", "Generalized Anxiety Disorder", "Panic Disorder", "PTSD", "OCD", "ADHD", "Schizophrenia", "Schizoaffective Disorder", "Substance Use Disorder", "Autism Spectrum Disorder", "Eating Disorder", "Sleep-Wake Disorder", "Other"].index(diag.get('primary_diagnosis'))
            )

            # Dynamic specifiers based on primary diagnosis
            specifiers = []
            if "Depressive" in primary_diagnosis:
                specifiers = ["Mild", "Moderate", "Severe", "With psychotic features", "In partial remission", "In full remission"]
            elif "Bipolar" in primary_diagnosis:
                specifiers = ["Most recent episode manic", "Most recent episode depressed", "Most recent episode mixed", "With rapid cycling"]
            elif "Anxiety" in primary_diagnosis or "Panic" in primary_diagnosis:
                specifiers = ["Mild", "Moderate", "Severe", "With agoraphobia", "Without agoraphobia"]

            primary_specifiers = create_enhanced_multiselect(
                "Diagnostic Specifiers",
                specifiers,
                default_values=diag.get('primary_specifiers', []),
                help_text="Select applicable specifiers for the primary diagnosis"
            ) if specifiers else []

        with col2:
            confidence_primary = st.select_slider(
                "Diagnostic Confidence - Primary",
                options=["Low", "Moderate", "High", "Very High"],
                value=diag.get('confidence_primary', "Moderate")
            )

            severity_rating = st.selectbox(
                "Overall Severity",
                ["", "Mild", "Moderate", "Severe", "Extreme"],
                index=0 if not diag.get('severity_rating') else ["", "Mild", "Moderate", "Severe", "Extreme"].index(diag.get('severity_rating'))
            )

        return {
            'primary_diagnosis': primary_diagnosis,
            'primary_specifiers': primary_specifiers,
            'confidence_primary': confidence_primary,
            'severity_rating': severity_rating
        }

    # Secondary and Comorbid Diagnoses
    def secondary_diagnoses_content():
        st.markdown("#### 🔄 Secondary & Comorbid Diagnoses")

        secondary_diagnoses = create_enhanced_multiselect(
            "Secondary/Comorbid Diagnoses",
            ["Major Depressive Disorder", "Generalized Anxiety Disorder", "PTSD",
             "Substance Use Disorder", "Personality Disorder", "ADHD", "OCD",
             "Eating Disorder", "Sleep Disorder", "Adjustment Disorder", "Other"],
            default_values=diag.get('secondary_diagnoses', [])
        )

        medical_diagnoses = create_enhanced_multiselect(
            "Relevant Medical Diagnoses",
            ["Diabetes", "Hypertension", "Thyroid disorder", "Cardiovascular disease",
             "Neurological condition", "Chronic pain", "Sleep apnea", "Autoimmune disorder", "Other"],
            default_values=diag.get('medical_diagnoses', [])
        )

        return {
            'secondary_diagnoses': secondary_diagnoses,
            'medical_diagnoses': medical_diagnoses
        }

    # Differential Diagnosis and Rule-Outs
    def differential_diagnosis_content():
        st.markdown("#### ❓ Differential Diagnosis & Rule-Outs")
        col1, col2 = st.columns(2)

        with col1:
            rule_out_diagnoses = create_enhanced_multiselect(
                "Rule Out/Differential Diagnoses",
                ["Bipolar Disorder", "Psychotic Disorder", "Personality Disorder",
                 "Medical condition", "Substance-induced", "Adjustment Disorder",
                 "Neurodevelopmental Disorder", "Trauma-related Disorder", "Other"],
                default_values=diag.get('rule_out_diagnoses', [])
            )

        with col2:
            diagnostic_certainty = st.selectbox(
                "Overall Diagnostic Certainty",
                ["", "Provisional", "Working diagnosis", "Confident", "Definitive"],
                index=0 if not diag.get('diagnostic_certainty') else ["", "Provisional", "Working diagnosis", "Confident", "Definitive"].index(diag.get('diagnostic_certainty'))
            )

        return {
            'rule_out_diagnoses': rule_out_diagnoses,
            'diagnostic_certainty': diagnostic_certainty
        }

    # Clinical Formulation and Reasoning
    def clinical_formulation_content():
        st.markdown("#### 📝 Clinical Formulation & Reasoning")

        formulation_notes = st.text_area(
            "Biopsychosocial Formulation",
            value=diag.get('formulation_notes', ''),
            height=120,
            placeholder="Comprehensive case conceptualization including biological, psychological, and social factors..."
        )

        clinical_reasoning = st.text_area(
            "Clinical Reasoning",
            value=diag.get('clinical_reasoning', ''),
            height=100,
            placeholder="Rationale for diagnostic decisions, supporting evidence, and clinical judgment..."
        )

        return {
            'formulation_notes': formulation_notes,
            'clinical_reasoning': clinical_reasoning
        }

    # Render all diagnostic formulation sections
    if create_form_section(
        "🎯 Primary Diagnosis & Specifiers",
        "primary_diagnosis_form",
        primary_diagnosis_content,
        expanded=True,
        help_text="Primary psychiatric diagnosis with applicable specifiers and confidence level."
    ):
        form_data = primary_diagnosis_content()
        diag.update(form_data)

    if create_form_section(
        "🔄 Secondary & Comorbid Diagnoses",
        "secondary_diagnoses_form",
        secondary_diagnoses_content,
        expanded=True,
        help_text="Additional psychiatric and medical diagnoses that impact treatment."
    ):
        form_data = secondary_diagnoses_content()
        diag.update(form_data)

    if create_form_section(
        "❓ Differential Diagnosis & Rule-Outs",
        "differential_diagnosis_form",
        differential_diagnosis_content,
        expanded=True,
        help_text="Diagnoses considered and ruled out during assessment process."
    ):
        form_data = differential_diagnosis_content()
        diag.update(form_data)

    if create_form_section(
        "📝 Clinical Formulation & Reasoning",
        "clinical_formulation_form",
        clinical_formulation_content,
        expanded=True,
        help_text="Comprehensive biopsychosocial formulation and clinical reasoning."
    ):
        form_data = clinical_formulation_content()
        diag.update(form_data)

def render_treatment_planning_section():
    """Render treatment planning section"""
    st.markdown('<div class="section-header">💊 Treatment Planning</div>', unsafe_allow_html=True)

    if 'treatment_planning' not in st.session_state.patient_data:
        st.session_state.patient_data['treatment_planning'] = {}

    treat = st.session_state.patient_data['treatment_planning']

    def treatment_content():
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Pharmacological Treatment**")
            medication_recommendations = st.text_area(
                "Medication Recommendations",
                value=treat.get('medication_recommendations', ''),
                height=100,
                placeholder="Specific medications, dosages, and rationale"
            )

            medication_monitoring = st.text_area(
                "Monitoring Requirements",
                value=treat.get('medication_monitoring', ''),
                height=80,
                placeholder="Lab monitoring, side effects to watch for"
            )

            st.markdown("**Psychosocial Interventions**")
            therapy_recommendations = create_enhanced_multiselect(
                "Therapy Recommendations",
                ["Individual therapy", "Group therapy", "Family therapy", "CBT", "DBT",
                 "EMDR", "Psychodynamic therapy", "Supportive therapy", "Other"],
                default_values=treat.get('therapy_recommendations', [])
            )

        with col2:
            st.markdown("**Treatment Goals**")
            short_term_goals = st.text_area(
                "Short-term Goals (1-3 months)",
                value=treat.get('short_term_goals', ''),
                height=100,
                placeholder="Immediate treatment objectives"
            )

            long_term_goals = st.text_area(
                "Long-term Goals (6-12 months)",
                value=treat.get('long_term_goals', ''),
                height=100,
                placeholder="Overall treatment outcomes"
            )

            st.markdown("**Additional Interventions**")
            other_interventions = create_enhanced_multiselect(
                "Other Interventions",
                ["Case management", "Peer support", "Vocational rehabilitation",
                 "Housing assistance", "Substance abuse treatment", "Medical referral", "Other"],
                default_values=treat.get('other_interventions', [])
            )

        treatment_setting = st.selectbox(
            "Recommended Treatment Setting",
            ["", "Outpatient", "Intensive outpatient", "Partial hospitalization",
             "Inpatient", "Residential", "Crisis intervention"],
            index=0 if not treat.get('treatment_setting') else ["", "Outpatient", "Intensive outpatient", "Partial hospitalization", "Inpatient", "Residential", "Crisis intervention"].index(treat.get('treatment_setting'))
        )

        return {
            'medication_recommendations': medication_recommendations,
            'medication_monitoring': medication_monitoring,
            'therapy_recommendations': therapy_recommendations,
            'short_term_goals': short_term_goals,
            'long_term_goals': long_term_goals,
            'other_interventions': other_interventions,
            'treatment_setting': treatment_setting
        }

    if create_form_section(
        "💊 Treatment Plan",
        "treatment_planning_form",
        treatment_content,
        expanded=True,
        help_text="Comprehensive treatment plan addressing all identified needs and goals."
    ):
        form_data = treatment_content()
        treat.update(form_data)

def render_follow_up_monitoring_section():
    """Render follow-up and monitoring section"""
    st.markdown('<div class="section-header">📅 Follow-up & Monitoring</div>', unsafe_allow_html=True)

    if 'follow_up_monitoring' not in st.session_state.patient_data:
        st.session_state.patient_data['follow_up_monitoring'] = {}

    follow = st.session_state.patient_data['follow_up_monitoring']

    def follow_up_content():
        col1, col2 = st.columns(2)

        with col1:
            follow_up_frequency = st.selectbox(
                "Follow-up Frequency",
                ["", "1 week", "2 weeks", "1 month", "3 months", "6 months", "As needed"],
                index=0 if not follow.get('follow_up_frequency') else ["", "1 week", "2 weeks", "1 month", "3 months", "6 months", "As needed"].index(follow.get('follow_up_frequency'))
            )

            monitoring_parameters = create_enhanced_multiselect(
                "Monitoring Parameters",
                ["Symptom severity", "Medication adherence", "Side effects", "Suicidal ideation",
                 "Functional improvement", "Lab values", "Substance use", "Treatment engagement"],
                default_values=follow.get('monitoring_parameters', [])
            )

            crisis_plan = st.text_area(
                "Crisis/Safety Plan",
                value=follow.get('crisis_plan', ''),
                height=100,
                placeholder="Emergency contacts, warning signs, coping strategies"
            )

        with col2:
            referrals_made = create_enhanced_multiselect(
                "Referrals Made",
                ["Primary care", "Psychiatry", "Psychology", "Social work", "Case management",
                 "Substance abuse treatment", "Medical specialist", "Crisis services", "None"],
                default_values=follow.get('referrals_made', [])
            )

            next_appointment = st.date_input(
                "Next Appointment Date",
                value=follow.get('next_appointment') if follow.get('next_appointment') else None
            )

            additional_notes = st.text_area(
                "Additional Follow-up Notes",
                value=follow.get('additional_notes', ''),
                height=100,
                placeholder="Any additional follow-up considerations"
            )

        return {
            'follow_up_frequency': follow_up_frequency,
            'monitoring_parameters': monitoring_parameters,
            'crisis_plan': crisis_plan,
            'referrals_made': referrals_made,
            'next_appointment': next_appointment,
            'additional_notes': additional_notes
        }

    if create_form_section(
        "📅 Follow-up Plan",
        "follow_up_form",
        follow_up_content,
        expanded=True,
        help_text="Establish clear follow-up plan and monitoring strategy for ongoing care."
    ):
        form_data = follow_up_content()
        follow.update(form_data)
