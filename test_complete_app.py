"""
Comprehensive Test Suite for Modular Psychiatric Assessment System

This script tests all functionality to ensure the modular app is as complete
as the original app2.py and that all features work correctly.
"""

import sys
import os
import sqlite3
import json
import datetime
from typing import Dict, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported successfully"""
    print("Testing imports...")
    
    try:
        import app_refactored
        print("✅ app_refactored imported successfully")
        
        import data_models
        print("✅ data_models imported successfully")
        
        import ui_components
        print("✅ ui_components imported successfully")
        
        import section_handlers
        print("✅ section_handlers imported successfully")
        
        import substance_section
        print("✅ substance_section imported successfully")
        
        import database
        print("✅ database imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_models():
    """Test data models and validation functions"""
    print("\nTesting data models...")
    
    from data_models import (
        DSM5_SUBSTANCE_CATEGORIES, 
        PSYCHIATRIC_MEDICATION_CLASSES,
        validate_substance_use_data,
        validate_medication_history_data,
        get_dsm5_severity,
        validate_comprehensive_assessment
    )
    
    # Test DSM-5 categories
    assert len(DSM5_SUBSTANCE_CATEGORIES) == 10, "Should have 10 substance categories"
    assert 'Alcohol' in DSM5_SUBSTANCE_CATEGORIES, "Should include Alcohol category"
    print("✅ DSM-5 substance categories loaded correctly")
    
    # Test psychiatric medications
    assert len(PSYCHIATRIC_MEDICATION_CLASSES) >= 8, "Should have at least 8 medication classes"
    assert 'Antidepressants' in PSYCHIATRIC_MEDICATION_CLASSES, "Should include Antidepressants"
    print("✅ Psychiatric medication classes loaded correctly")
    
    # Test severity calculation
    assert get_dsm5_severity(0) == "No disorder"
    assert get_dsm5_severity(2) == "Mild"
    assert get_dsm5_severity(4) == "Moderate"
    assert get_dsm5_severity(6) == "Severe"
    print("✅ DSM-5 severity calculation working correctly")
    
    # Test validation functions
    test_substance_data = {
        'Alcohol': {
            'used': True,
            'withdrawal': True,
            'tolerance': True,
            'age_onset': 18,
            'dsm5_criteria': ['Tolerance', 'Withdrawal']
        }
    }
    
    errors = validate_substance_use_data(test_substance_data)
    assert isinstance(errors, list), "Validation should return a list"
    print("✅ Substance use validation working correctly")
    
    return True

def test_database_functionality():
    """Test database operations"""
    print("\nTesting database functionality...")
    
    from database import (
        create_tables,
        save_patient_data,
        save_assessment_data,
        get_all_patients,
        db_manager
    )
    
    # Test database creation
    try:
        create_tables()
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False
    
    # Test database connection
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            assert 'patients' in table_names, "patients table should exist"
            assert 'assessments' in table_names, "assessments table should exist"
            print("✅ Database structure verified")
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False
    
    return True

def test_section_handlers():
    """Test that all section handler functions exist"""
    print("\nTesting section handlers...")
    
    from section_handlers import (
        render_demographics_section,
        render_chief_complaint_section,
        render_history_present_illness_section,
        render_past_psychiatric_history_section,
        render_past_medical_history_section,
        render_family_history_section,
        render_social_developmental_history_section,
        render_mental_state_examination_section,
        render_cognitive_assessment_section,
        render_risk_assessment_section,
        render_laboratory_investigations_section,
        render_clinical_scales_section,
        render_diagnostic_formulation_section,
        render_treatment_planning_section,
        render_follow_up_monitoring_section
    )
    
    # Check that all functions are callable
    section_functions = [
        render_demographics_section,
        render_chief_complaint_section,
        render_history_present_illness_section,
        render_past_psychiatric_history_section,
        render_past_medical_history_section,
        render_family_history_section,
        render_social_developmental_history_section,
        render_mental_state_examination_section,
        render_cognitive_assessment_section,
        render_risk_assessment_section,
        render_laboratory_investigations_section,
        render_clinical_scales_section,
        render_diagnostic_formulation_section,
        render_treatment_planning_section,
        render_follow_up_monitoring_section
    ]
    
    for func in section_functions:
        assert callable(func), f"{func.__name__} should be callable"
    
    print(f"✅ All {len(section_functions)} section handlers are available and callable")
    return True

def test_ui_components():
    """Test UI components"""
    print("\nTesting UI components...")
    
    from ui_components import (
        render_modern_css,
        create_form_section,
        create_substance_card,
        render_severity_indicator,
        create_enhanced_multiselect,
        show_clinical_guidance
    )
    
    # Check that all functions are callable
    ui_functions = [
        render_modern_css,
        create_form_section,
        create_substance_card,
        render_severity_indicator,
        create_enhanced_multiselect,
        show_clinical_guidance
    ]
    
    for func in ui_functions:
        assert callable(func), f"{func.__name__} should be callable"
    
    print(f"✅ All {len(ui_functions)} UI components are available and callable")
    return True

def test_substance_section():
    """Test substance use section"""
    print("\nTesting substance use section...")
    
    from substance_section import render_substance_use_section
    
    assert callable(render_substance_use_section), "render_substance_use_section should be callable"
    print("✅ Substance use section is available and callable")
    return True

def run_comprehensive_tests():
    """Run all tests and report results"""
    print("🧪 Starting Comprehensive Test Suite for Modular Psychiatric Assessment System")
    print("=" * 80)
    
    tests = [
        ("Import Tests", test_imports),
        ("Data Models Tests", test_data_models),
        ("Database Tests", test_database_functionality),
        ("Section Handlers Tests", test_section_handlers),
        ("UI Components Tests", test_ui_components),
        ("Substance Section Tests", test_substance_section)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! The modular app is complete and functional.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
