"""
Test script for the refactored psychiatric assessment application
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported successfully"""
    print("Testing imports...")
    
    try:
        import data_models
        print("✅ data_models imported successfully")
        
        import ui_components
        print("✅ ui_components imported successfully")
        
        import substance_section
        print("✅ substance_section imported successfully")
        
        import database
        print("✅ database imported successfully")
        
        import section_handlers
        print("✅ section_handlers imported successfully")
        
        import app_refactored
        print("✅ app_refactored imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_data_models():
    """Test data models functionality"""
    print("\nTesting data models...")
    
    try:
        from data_models import DSM5_SUBSTANCE_CATEGORIES, PSYCHIATRIC_MEDICATION_CLASSES, get_dsm5_severity
        
        # Test DSM-5 categories
        assert len(DSM5_SUBSTANCE_CATEGORIES) == 10, "Should have 10 substance categories"
        assert "Alcohol" in DSM5_SUBSTANCE_CATEGORIES, "Should include Alcohol category"
        assert "routes" in DSM5_SUBSTANCE_CATEGORIES["Alcohol"], "Should include routes for Alcohol"
        
        # Test medication classes
        assert len(PSYCHIATRIC_MEDICATION_CLASSES) >= 7, "Should have at least 7 medication classes"
        assert "Antidepressants" in PSYCHIATRIC_MEDICATION_CLASSES, "Should include Antidepressants"
        
        # Test severity calculation
        assert get_dsm5_severity(0) == "No disorder"
        assert get_dsm5_severity(2) == "Mild"
        assert get_dsm5_severity(4) == "Moderate"
        assert get_dsm5_severity(6) == "Severe"
        
        print("✅ Data models tests passed")
        return True
    except Exception as e:
        print(f"❌ Data models test failed: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from database import create_connection, create_tables
        
        # Test database connection
        conn = create_connection()
        if conn:
            conn.close()
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        # Test table creation
        create_tables()
        print("✅ Database tables created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_substance_enhancements():
    """Test substance section enhancements"""
    print("\nTesting substance section enhancements...")
    
    try:
        from data_models import DSM5_SUBSTANCE_CATEGORIES, ROUTE_OPTIONS, DURATION_OPTIONS, FREQUENCY_OPTIONS
        
        # Test enhanced substance categories have routes
        for category, data in DSM5_SUBSTANCE_CATEGORIES.items():
            assert "routes" in data, f"{category} should have routes defined"
            assert len(data["routes"]) > 0, f"{category} should have at least one route"
        
        # Test new options are available
        assert len(ROUTE_OPTIONS) >= 6, "Should have multiple route options"
        assert len(DURATION_OPTIONS) >= 7, "Should have multiple duration options"
        assert len(FREQUENCY_OPTIONS) >= 6, "Should have multiple frequency options"
        
        # Test specific enhancements
        assert "IV" in ROUTE_OPTIONS, "Should include IV route"
        assert "Nasal" in ROUTE_OPTIONS, "Should include Nasal route"
        assert "1-6 months" in DURATION_OPTIONS, "Should include specific duration ranges"
        
        print("✅ Substance section enhancements verified")
        return True
    except Exception as e:
        print(f"❌ Substance enhancements test failed: {e}")
        return False

def test_ui_components():
    """Test UI components functionality"""
    print("\nTesting UI components...")
    
    try:
        from ui_components import render_modern_css, create_form_section, render_severity_indicator
        
        # Test that functions exist and are callable
        assert callable(render_modern_css), "render_modern_css should be callable"
        assert callable(create_form_section), "create_form_section should be callable"
        assert callable(render_severity_indicator), "render_severity_indicator should be callable"
        
        print("✅ UI components tests passed")
        return True
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 Running Psychiatric Assessment App Tests\n")
    
    tests = [
        test_imports,
        test_data_models,
        test_database,
        test_substance_enhancements,
        test_ui_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactored application is ready.")
        return True
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
