"""
Database Operations for Psychiatric Assessment System

This module handles all database operations including connection management,
data saving, and retrieval with proper error handling and connection pooling.
"""

import sqlite3
import datetime
import json
import logging
import threading
import time
from contextlib import contextmanager
from typing import Dict, Any, List, Optional
from sqlite3 import Error
import streamlit as st
from data_models import safe_json_dumps

# Enhanced Database Manager
class DatabaseManager:
    """Enhanced database manager with connection pooling and retry logic"""
    
    def __init__(self, db_path: str = 'psychiatric_assessments.db', 
                 max_connections: int = 5, timeout: float = 30.0):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self.connection_pool = []
        self.pool_lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Initialize database
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database with proper settings"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=self.timeout)
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = -64000")  # 64MB cache
            conn.execute("PRAGMA busy_timeout = 30000")  # 30 second busy timeout
            
            # Create tables using the function defined below
            cursor = conn.cursor()
            self._create_tables_with_cursor(cursor)
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup"""
        conn = None
        try:
            # Try to get connection from pool
            with self.pool_lock:
                if self.connection_pool:
                    conn = self.connection_pool.pop()
                else:
                    conn = sqlite3.connect(
                        self.db_path, 
                        timeout=self.timeout,
                        check_same_thread=False
                    )
                    conn.execute("PRAGMA foreign_keys = ON")
                    conn.execute("PRAGMA busy_timeout = 30000")
            
            yield conn
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            # Return connection to pool or close
            if conn:
                try:
                    with self.pool_lock:
                        if len(self.connection_pool) < self.max_connections:
                            self.connection_pool.append(conn)
                        else:
                            conn.close()
                except Exception as e:
                    self.logger.error(f"Error returning connection to pool: {e}")
                    try:
                        conn.close()
                    except:
                        pass
    
    def execute_with_retry(self, operation_func, max_retries: int = 3):
        """Execute database operation with retry logic"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    return operation_func(conn)
            
            except (sqlite3.OperationalError, sqlite3.DatabaseError) as e:
                last_exception = e
                if attempt < max_retries - 1:
                    # Exponential backoff
                    wait_time = 0.1 * (2 ** attempt)
                    time.sleep(wait_time)
                    self.logger.warning(f"Database operation failed, retrying in {wait_time}s: {e}")
                else:
                    self.logger.error(f"Database operation failed after {max_retries} attempts: {e}")
            
            except Exception as e:
                self.logger.error(f"Unexpected database error: {e}")
                raise
        
        raise last_exception

    def _create_tables_with_cursor(self, cursor):
        """Create tables using cursor"""
        # Enhanced patients table with constraints
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            patient_id TEXT PRIMARY KEY,
            age INTEGER CHECK (age >= 0 AND age <= 150),
            gender TEXT,
            sex_assigned TEXT,
            marital_status TEXT,
            children TEXT,
            education TEXT,
            occupation TEXT,
            employment_status TEXT,
            ethnicity TEXT,
            living_situation TEXT,
            housing_stability TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Enhanced assessments table with constraints
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS assessments (
            assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            chief_complaint TEXT,
            history_present_illness TEXT,
            past_psychiatric_history TEXT,
            past_medical_history TEXT,
            family_history TEXT,
            social_developmental_history TEXT,
            substance_use TEXT,
            mental_state_examination TEXT,
            cognitive_assessment TEXT,
            risk_assessment TEXT,
            laboratory_investigations TEXT,
            clinical_scales TEXT,
            diagnostic_formulation TEXT,
            treatment_planning TEXT,
            follow_up_monitoring TEXT,
            suicide_risk_level TEXT CHECK (suicide_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
            primary_diagnosis TEXT,
            phq9_score INTEGER CHECK (phq9_score >= 0 AND phq9_score <= 27),
            gad7_score INTEGER CHECK (gad7_score >= 0 AND gad7_score <= 21),
            duration_minutes REAL CHECK (duration_minutes >= 0),
            completion_percentage REAL CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
            data_json TEXT,
            created_by TEXT DEFAULT 'system',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
        )
        ''')

        # Create indexes for faster queries
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON assessments (patient_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_assessment_date ON assessments (assessment_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_primary_diagnosis ON assessments (primary_diagnosis)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_suicide_risk ON assessments (suicide_risk_level)')

# Global database manager instance
db_manager = DatabaseManager()

def create_connection():
    """Create a database connection to the SQLite database with improved error handling"""
    try:
        conn = sqlite3.connect(
            'psychiatric_assessments.db',
            timeout=30.0,  # 30 second timeout to prevent locking
            check_same_thread=False
        )
        # Enable foreign key constraints and WAL mode for better concurrency
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")

        # Fix datetime adapter deprecation warning
        sqlite3.register_adapter(datetime.datetime, lambda val: val.isoformat())
        sqlite3.register_converter("timestamp", lambda val: datetime.datetime.fromisoformat(val.decode()))
        return conn
    except Error as e:
        st.error(f"Database error: {e}")
        return None

def create_tables_with_connection(conn):
    """Create tables with an existing connection"""
    try:
        cursor = conn.cursor()
        db_manager._create_tables_with_cursor(cursor)
        conn.commit()
    except Error as e:
        st.error(f"Error creating tables: {e}")

def create_tables():
    """Create tables if they don't exist with enhanced constraints"""
    conn = create_connection()
    if conn is not None:
        try:
            create_tables_with_connection(conn)
        finally:
            conn.close()

def save_patient_data(patient_data):
    """Save patient demographics to the database"""
    def save_operation(conn):
        cursor = conn.cursor()
        
        # Extract demographics
        demographics = patient_data.get('demographics', {})
        
        cursor.execute('''
        INSERT OR REPLACE INTO patients 
        (patient_id, age, gender, sex_assigned, marital_status, children, education, 
         occupation, employment_status, ethnicity, living_situation, housing_stability, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            st.session_state.patient_id,
            demographics.get('age'),
            demographics.get('gender'),
            demographics.get('sex_assigned'),
            demographics.get('marital_status'),
            demographics.get('children'),
            demographics.get('education'),
            demographics.get('occupation'),
            demographics.get('employment_status'),
            demographics.get('ethnicity'),
            demographics.get('living_situation'),
            demographics.get('housing_stability'),
            datetime.datetime.now()
        ))
        
        conn.commit()
        return True
    
    try:
        return db_manager.execute_with_retry(save_operation)
    except Exception as e:
        st.error(f"Error saving patient data: {e}")
        return False

def save_assessment_data(patient_data):
    """Save complete assessment data to the database"""
    def save_operation(conn):
        cursor = conn.cursor()
        
        # Calculate completion percentage
        total_sections = 16
        completed_sections = len([k for k in patient_data.keys() if patient_data.get(k)])
        completion_percentage = (completed_sections / total_sections) * 100
        
        # Calculate duration
        start_time = st.session_state.get('assessment_start_time', datetime.datetime.now())
        duration_minutes = (datetime.datetime.now() - start_time).total_seconds() / 60
        
        # Extract key data for structured fields
        chief_complaint = patient_data.get('chief_complaint', {})
        risk_assessment = patient_data.get('risk_assessment', {})
        clinical_scales = patient_data.get('clinical_scales', {})
        
        cursor.execute('''
        INSERT OR REPLACE INTO assessments 
        (patient_id, assessment_date, chief_complaint, history_present_illness, 
         past_psychiatric_history, past_medical_history, family_history, 
         social_developmental_history, substance_use, mental_state_examination,
         cognitive_assessment, risk_assessment, laboratory_investigations,
         clinical_scales, diagnostic_formulation, treatment_planning,
         follow_up_monitoring, suicide_risk_level, primary_diagnosis,
         phq9_score, gad7_score, duration_minutes, completion_percentage,
         data_json, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            st.session_state.patient_id,
            datetime.datetime.now(),
            safe_json_dumps(chief_complaint),
            safe_json_dumps(patient_data.get('history_present_illness', {})),
            safe_json_dumps(patient_data.get('past_psychiatric_history', {})),
            safe_json_dumps(patient_data.get('past_medical_history', {})),
            safe_json_dumps(patient_data.get('family_history', {})),
            safe_json_dumps(patient_data.get('social_developmental_history', {})),
            safe_json_dumps(patient_data.get('substance_use', {})),
            safe_json_dumps(patient_data.get('mental_state_examination', {})),
            safe_json_dumps(patient_data.get('cognitive_assessment', {})),
            safe_json_dumps(risk_assessment),
            safe_json_dumps(patient_data.get('laboratory_investigations', {})),
            safe_json_dumps(clinical_scales),
            safe_json_dumps(patient_data.get('diagnostic_formulation', {})),
            safe_json_dumps(patient_data.get('treatment_planning', {})),
            safe_json_dumps(patient_data.get('follow_up_monitoring', {})),
            risk_assessment.get('suicide_risk_level'),
            patient_data.get('diagnostic_formulation', {}).get('primary_diagnosis'),
            clinical_scales.get('phq9_total'),
            clinical_scales.get('gad7_total'),
            duration_minutes,
            completion_percentage,
            safe_json_dumps(patient_data),
            datetime.datetime.now()
        ))
        
        conn.commit()
        return True
    
    try:
        return db_manager.execute_with_retry(save_operation)
    except Exception as e:
        st.error(f"Error saving assessment data: {e}")
        return False

def load_patient_assessments(patient_id: str) -> List[Dict[str, Any]]:
    """Load all assessments for a patient"""
    def load_operation(conn):
        cursor = conn.cursor()
        cursor.execute('''
        SELECT * FROM assessments 
        WHERE patient_id = ? 
        ORDER BY assessment_date DESC
        ''', (patient_id,))
        
        columns = [description[0] for description in cursor.description]
        assessments = []
        
        for row in cursor.fetchall():
            assessment = dict(zip(columns, row))
            # Parse JSON data
            if assessment['data_json']:
                try:
                    assessment['parsed_data'] = json.loads(assessment['data_json'])
                except json.JSONDecodeError:
                    assessment['parsed_data'] = {}
            assessments.append(assessment)
        
        return assessments
    
    try:
        return db_manager.execute_with_retry(load_operation)
    except Exception as e:
        st.error(f"Error loading assessments: {e}")
        return []

def get_all_patients() -> List[Dict[str, Any]]:
    """Get all patients from the database"""
    def get_operation(conn):
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patients ORDER BY updated_at DESC')
        
        columns = [description[0] for description in cursor.description]
        patients = []
        
        for row in cursor.fetchall():
            patients.append(dict(zip(columns, row)))
        
        return patients
    
    try:
        return db_manager.execute_with_retry(get_operation)
    except Exception as e:
        st.error(f"Error getting patients: {e}")
        return []
