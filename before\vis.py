import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Page config
st.set_page_config(
    page_title="Medical History App - Style Testing",
    page_icon="📋",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Sample medical data
@st.cache_data
def generate_medical_data():
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', end='2024-08-01', freq='D')
    
    conditions = ['Hypertension', 'Diabetes', 'Asthma', 'Arthritis', 'Depression', 'Anxiety']
    age_groups = ['18-30', '31-45', '46-60', '61-75', '75+']
    
    data = {
        'Date': dates,
        'Patients_Seen': np.random.poisson(45, len(dates)),
        'Avg_Age': np.random.normal(52, 15, len(dates)).clip(18, 95),
        'Satisfaction_Score': np.random.normal(4.1, 0.6, len(dates)).clip(1, 5),
        'Condition': np.random.choice(conditions, len(dates)),
        'Age_Group': np.random.choice(age_groups, len(dates)),
        'Visit_Duration': np.random.normal(22, 8, len(dates)).clip(5, 60)
    }
    return pd.DataFrame(data)

df = generate_medical_data()

# Available matplotlib styles
available_styles = [
    'default',
    'seaborn-v0_8',
    'seaborn-v0_8-whitegrid',
    'seaborn-v0_8-darkgrid', 
    'ggplot',
    'bmh',
    'fivethirtyeight',
    'grayscale'
]

# Custom color palettes for medical data
medical_palettes = {
    'Professional Blue': ['#1f4e79', '#2e6da4', '#337ab7', '#428bca', '#5bc0de'],
    'Medical Safe': ['#2c3e50', '#3498db', '#27ae60', '#f39c12', '#e74c3c'],
    'Accessible High Contrast': ['#000080', '#008000', '#800000', '#800080', '#008080'],
    'Warm Clinical': ['#8B4513', '#D2691E', '#CD853F', '#DEB887', '#F5DEB3'],
    'Cool Clinical': ['#2F4F4F', '#708090', '#778899', '#B0C4DE', '#E6E6FA'],
    'Colorblind Friendly': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
}

# Streamlit theme configurations
streamlit_themes = {
    'Light Professional': {
        'css': '''
        <style>
        .stApp { background-color: #ffffff; }
        .main-header { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px;
            border-left: 4px solid #2c3e50;
            margin-bottom: 20px;
        }
        .metric-card { 
            background: #ffffff; 
            padding: 20px; 
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #dee2e6;
        }
        </style>
        '''
    },
    'High Contrast Medical': {
        'css': '''
        <style>
        .stApp { background-color: #f5f5f5; }
        .main-header { 
            background: #ffffff; 
            padding: 20px; 
            border-radius: 8px;
            border: 2px solid #2c3e50;
            margin-bottom: 20px;
        }
        .metric-card { 
            background: #ffffff; 
            padding: 20px; 
            border-radius: 8px;
            border: 1px solid #2c3e50;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        </style>
        '''
    },
    'Soft Clinical': {
        'css': '''
        <style>
        .stApp { background-color: #fafbfc; }
        .main-header { 
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); 
            padding: 20px; 
            border-radius: 12px;
            border: 1px solid #e1bee7;
            margin-bottom: 20px;
        }
        .metric-card { 
            background: #ffffff; 
            padding: 20px; 
            border-radius: 10px;
            border-left: 4px solid #7986cb;
            box-shadow: 0 2px 8px rgba(121, 134, 203, 0.1);
        }
        </style>
        '''
    },
    'Clean Readable': {
        'css': '''
        <style>
        .stApp { background-color: #ffffff; }
        .main-header { 
            background: #f8f9fa; 
            padding: 25px; 
            border-radius: 6px;
            border-bottom: 3px solid #007bff;
            margin-bottom: 20px;
        }
        .metric-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 6px;
            border: 1px solid #ced4da;
        }
        h1, h2, h3 { color: #212529 !important; }
        </style>
        '''
    }
}

# Sidebar controls
st.sidebar.title("🎨 Style Testing Lab")
st.sidebar.markdown("**Test different visual approaches for medical history taking**")

# Theme selection
selected_streamlit_theme = st.sidebar.selectbox(
    "📱 App Theme:",
    list(streamlit_themes.keys())
)

# Matplotlib style selection  
selected_mpl_style = st.sidebar.selectbox(
    "📊 Chart Style:",
    available_styles
)

# Color palette selection
selected_palette = st.sidebar.selectbox(
    "🎨 Color Palette:",
    list(medical_palettes.keys())
)

# Apply selected Streamlit theme
st.markdown(streamlit_themes[selected_streamlit_theme]['css'], unsafe_allow_html=True)

# Set matplotlib style
plt.style.use(selected_mpl_style)

# Header
st.markdown(f"""
<div class="main-header">
    <h1 style="margin: 0; font-size: 28px;">📋 Medical History Dashboard</h1>
    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.8;">
        <strong>Theme:</strong> {selected_streamlit_theme} | 
        <strong>Chart Style:</strong> {selected_mpl_style} | 
        <strong>Palette:</strong> {selected_palette}
    </p>
</div>
""", unsafe_allow_html=True)

# Key metrics
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.markdown('<div class="metric-card">', unsafe_allow_html=True)
    st.metric("👥 Total Patients", "10,247", "↗️ 8.2%")
    st.markdown('</div>', unsafe_allow_html=True)

with col2:
    st.markdown('<div class="metric-card">', unsafe_allow_html=True)
    st.metric("⭐ Satisfaction", "4.1/5.0", "↗️ 0.2")
    st.markdown('</div>', unsafe_allow_html=True)

with col3:
    st.markdown('<div class="metric-card">', unsafe_allow_html=True)
    st.metric("⏱️ Avg Visit Time", "22 min", "↘️ 3 min")
    st.markdown('</div>', unsafe_allow_html=True)

with col4:
    st.markdown('<div class="metric-card">', unsafe_allow_html=True)
    st.metric("🏥 Active Conditions", "2,891", "↗️ 145")
    st.markdown('</div>', unsafe_allow_html=True)

# Chart demonstrations
st.markdown("---")

# Create tabs for different chart types
tab1, tab2, tab3, tab4 = st.tabs(["📊 Style Comparison", "📈 Time Series", "🔍 Distribution Analysis", "📋 Summary Tables"])

with tab1:
    st.subheader(f"Chart Style: {selected_mpl_style}")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Condition Frequency (Bar Chart)**")
        fig, ax = plt.subplots(figsize=(10, 6))
        condition_counts = df['Condition'].value_counts()
        colors = medical_palettes[selected_palette][:len(condition_counts)]
        
        bars = ax.bar(condition_counts.index, condition_counts.values, color=colors)
        ax.set_title('Most Common Conditions', fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Medical Conditions', fontsize=12)
        ax.set_ylabel('Number of Cases', fontsize=12)
        plt.xticks(rotation=45, ha='right')
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        st.pyplot(fig, clear_figure=True)
    
    with col2:
        st.write("**Age Group Distribution (Pie Chart)**")
        fig, ax = plt.subplots(figsize=(8, 8))
        age_counts = df['Age_Group'].value_counts()
        
        colors = medical_palettes[selected_palette][:len(age_counts)]
        wedges, texts, autotexts = ax.pie(age_counts.values, labels=age_counts.index, 
                                         autopct='%1.1f%%', colors=colors, startangle=90)
        
        # Make text more readable
        for text in texts:
            text.set_fontsize(12)
            text.set_fontweight('bold')
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(10)
        
        ax.set_title('Patient Age Distribution', fontsize=14, fontweight='bold', pad=20)
        st.pyplot(fig, clear_figure=True)

with tab2:
    st.subheader("Time Series Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Daily Patient Volume**")
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Smooth the data for better visualization
        weekly_avg = df.set_index('Date')['Patients_Seen'].rolling(window=7).mean()
        
        ax.plot(weekly_avg.index, weekly_avg.values, 
               color=medical_palettes[selected_palette][0], linewidth=3, alpha=0.8)
        ax.fill_between(weekly_avg.index, weekly_avg.values, alpha=0.3, 
                       color=medical_palettes[selected_palette][0])
        
        ax.set_title('Weekly Average Patient Volume', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Patients per Day', fontsize=12)
        ax.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        st.pyplot(fig, clear_figure=True)
    
    with col2:
        st.write("**Satisfaction Trends**")
        fig, ax = plt.subplots(figsize=(12, 6))
        
        monthly_satisfaction = df.groupby(df['Date'].dt.to_period('M'))['Satisfaction_Score'].mean()
        
        ax.plot(range(len(monthly_satisfaction)), monthly_satisfaction.values, 
               marker='o', color=medical_palettes[selected_palette][1], 
               linewidth=3, markersize=8, markerfacecolor='white', 
               markeredgecolor=medical_palettes[selected_palette][1], markeredgewidth=2)
        
        ax.set_title('Monthly Average Satisfaction Score', fontsize=14, fontweight='bold')
        ax.set_xlabel('Month', fontsize=12)
        ax.set_ylabel('Satisfaction Score', fontsize=12)
        ax.set_ylim(3.5, 4.5)
        ax.grid(True, alpha=0.3)
        
        # Set month labels
        month_labels = [str(period) for period in monthly_satisfaction.index]
        ax.set_xticks(range(len(month_labels)))
        ax.set_xticklabels(month_labels, rotation=45)
        
        plt.tight_layout()
        st.pyplot(fig, clear_figure=True)

with tab3:
    st.subheader("Distribution Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Visit Duration by Age Group**")
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create box plot
        age_order = ['18-30', '31-45', '46-60', '61-75', '75+']
        box_data = [df[df['Age_Group'] == age]['Visit_Duration'].values for age in age_order]
        
        bp = ax.boxplot(box_data, labels=age_order, patch_artist=True)
        
        # Color the boxes
        colors = medical_palettes[selected_palette]
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax.set_title('Visit Duration Distribution by Age Group', fontsize=14, fontweight='bold')
        ax.set_xlabel('Age Group', fontsize=12)
        ax.set_ylabel('Visit Duration (minutes)', fontsize=12)
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        st.pyplot(fig, clear_figure=True)
    
    with col2:
        st.write("**Satisfaction Score Histogram**")
        fig, ax = plt.subplots(figsize=(10, 6))
        
        ax.hist(df['Satisfaction_Score'], bins=20, color=medical_palettes[selected_palette][2], 
               alpha=0.7, edgecolor='black', linewidth=1)
        
        ax.set_title('Patient Satisfaction Score Distribution', fontsize=14, fontweight='bold')
        ax.set_xlabel('Satisfaction Score', fontsize=12)
        ax.set_ylabel('Number of Visits', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Add mean line
        mean_satisfaction = df['Satisfaction_Score'].mean()
        ax.axvline(mean_satisfaction, color='red', linestyle='--', linewidth=2, 
                  label=f'Mean: {mean_satisfaction:.2f}')
        ax.legend()
        
        plt.tight_layout()
        st.pyplot(fig, clear_figure=True)

with tab4:
    st.subheader("Data Summary")
    
    # Create summary statistics
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**📊 Key Statistics**")
        summary_stats = pd.DataFrame({
            'Metric': ['Total Patients', 'Avg Age', 'Avg Satisfaction', 'Avg Visit Duration', 'Most Common Condition'],
            'Value': [
                f"{df['Patients_Seen'].sum():,}",
                f"{df['Avg_Age'].mean():.1f} years",
                f"{df['Satisfaction_Score'].mean():.2f}/5.0",
                f"{df['Visit_Duration'].mean():.1f} minutes",
                df['Condition'].mode()[0]
            ]
        })
        st.dataframe(summary_stats, hide_index=True, use_container_width=True)
    
    with col2:
        st.write("**🎯 Top Conditions**")
        condition_summary = df['Condition'].value_counts().head().reset_index()
        condition_summary.columns = ['Condition', 'Cases']
        condition_summary['Percentage'] = (condition_summary['Cases'] / df.shape[0] * 100).round(1)
        st.dataframe(condition_summary, hide_index=True, use_container_width=True)

# Style comparison section
st.markdown("---")
st.subheader("📝 Current Style Analysis")

col1, col2 = st.columns(2)

with col1:
    st.markdown(f"""
    **🎨 Theme Combination:**
    - **App Theme:** {selected_streamlit_theme}
    - **Chart Style:** {selected_mpl_style}  
    - **Color Palette:** {selected_palette}
    
    **✅ Readability Features:**
    - High contrast text
    - Clear font weights
    - Consistent spacing
    - Professional color choices
    """)

with col2:
    # Show color palette
    st.write("**Current Color Palette:**")
    palette_colors = medical_palettes[selected_palette]
    
    color_html = ""
    for i, color in enumerate(palette_colors):
        color_html += f'''
        <div style="display: inline-block; width: 50px; height: 30px; 
                   background-color: {color}; margin: 2px; border: 1px solid #ccc;
                   border-radius: 4px;"></div>
        '''
    
    st.markdown(color_html, unsafe_allow_html=True)
    
    # Show hex codes
    st.code('\n'.join([f"Color {i+1}: {color}" for i, color in enumerate(palette_colors)]))

# Recommendations
with st.expander("💡 Style Recommendations"):
    st.markdown(f"""
    **For Medical History Apps:**
    
    **Best Readable Combinations:**
    1. **Light Professional** + **seaborn-v0_8-whitegrid** + **Professional Blue**
    2. **High Contrast Medical** + **bmh** + **Accessible High Contrast**  
    3. **Clean Readable** + **fivethirtyeight** + **Medical Safe**
    
    **Current Selection Analysis:**
    - **{selected_streamlit_theme}**: {"✅ Good choice" if "High Contrast" in selected_streamlit_theme or "Light" in selected_streamlit_theme else "⚠️ Consider lighter options"}
    - **{selected_mpl_style}**: {"✅ Excellent readability" if selected_mpl_style in ['seaborn-v0_8-whitegrid', 'bmh', 'fivethirtyeight'] else "✅ Good style"}
    - **{selected_palette}**: {"✅ Medical-appropriate colors" if "Medical" in selected_palette or "Professional" in selected_palette else "✅ Good color choice"}
    
    **Avoid:**
    - Dark backgrounds with dark text
    - Low contrast color combinations  
    - Overly bright or neon colors
    - More than 5-6 colors in one palette
    """)

st.sidebar.markdown("---")
st.sidebar.info("""
💡 **Testing Tips:**
- Try different combinations
- Check readability on mobile
- Test with colorblind simulation
- Consider your users' environment
- Medical professionals often prefer clean, professional looks
""")